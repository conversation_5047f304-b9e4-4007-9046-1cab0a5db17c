/**
 * HTTP模块配置和使用示例
 * 展示baseUrl配置、拦截器、默认请求头等功能
 */

document.addEventListener('DOMContentLoaded', async () => {
    console.log('=== HTTP模块配置功能演示 ===');
    
    // 1. 基础配置
    console.log('\n1. 基础配置演示:');
    
    // 配置基础URL和默认请求头
    Http.configure({
        baseUrl: 'https://api.example.com',
        defaultHeaders: {
            'Content-Type': 'application/json',
            'X-App-Version': '1.0.0'
        },
        defaultTimeout: 15000
    });
    
    console.log('基础URL:', Http.getBaseUrl());
    console.log('默认请求头:', Http.getDefaultHeaders());
    
    // 2. URL构建演示
    console.log('\n2. URL构建演示:');
    
    const urls = [
        '/api/users',           // 相对路径
        'api/users',            // 相对路径（无前导斜杠）
        '/api/users/123',       // 相对路径
        'https://other.com/api' // 绝对路径
    ];
    
    urls.forEach(url => {
        console.log(`${url} -> ${Http.buildUrl(url)}`);
    });
    
    // 3. 请求拦截器演示
    console.log('\n3. 请求拦截器演示:');
    
    // 添加认证拦截器
    Http.addRequestInterceptor(async (config) => {
        console.log('请求拦截器: 添加认证头');
        config.headers['Authorization'] = 'Bearer fake-token-123';
        return config;
    });
    
    // 添加日志拦截器
    Http.addRequestInterceptor(async (config) => {
        console.log(`请求拦截器: ${config.method} ${config.url}`);
        return config;
    });
    
    // 4. 响应拦截器演示
    console.log('\n4. 响应拦截器演示:');
    
    // 添加响应日志拦截器
    Http.addResponseInterceptor(async (response) => {
        console.log(`响应拦截器: ${response.status} ${response.statusText}`);
        return response;
    });
    
    // 添加错误处理拦截器
    Http.addResponseInterceptor(async (response) => {
        if (!response.ok) {
            console.warn('响应拦截器: 检测到错误响应', response.status);
        }
        return response;
    });
    
    // 5. 实际请求演示
    console.log('\n5. 实际请求演示:');
    
    try {
        // 使用相对路径发起请求
        console.log('发起GET请求...');
        const response = await Http.get('/users/1', {
            headers: {
                'X-Custom-Header': 'custom-value'
            }
        });
        
        console.log('请求成功:', response.status);
        
    } catch (error) {
        console.log('请求失败（预期的）:', error.message);
    }
    
    // 6. 不同环境配置演示
    console.log('\n6. 环境配置演示:');
    
    // 开发环境配置
    const devConfig = {
        baseUrl: 'http://localhost:3000',
        defaultHeaders: {
            'X-Environment': 'development'
        }
    };
    
    // 生产环境配置
    const prodConfig = {
        baseUrl: 'https://api.production.com',
        defaultHeaders: {
            'X-Environment': 'production'
        }
    };
    
    // 根据环境切换配置
    const isDevelopment = window.location.hostname === 'localhost';
    const config = isDevelopment ? devConfig : prodConfig;
    
    console.log('当前环境:', isDevelopment ? '开发' : '生产');
    Http.configure(config);
    console.log('更新后的基础URL:', Http.getBaseUrl());
    
    // 7. 批量请求演示
    console.log('\n7. 批量请求演示:');
    
    const endpoints = ['/users', '/posts', '/comments'];
    const requests = endpoints.map(endpoint => 
        Http.get(endpoint).catch(error => ({ error: error.message }))
    );
    
    try {
        const results = await Promise.allSettled(requests);
        console.log('批量请求完成:', results.length, '个请求');
        
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                console.log(`请求 ${endpoints[index]}: 成功`);
            } else {
                console.log(`请求 ${endpoints[index]}: 失败 -`, result.reason.message);
            }
        });
    } catch (error) {
        console.log('批量请求失败:', error.message);
    }
    
    // 8. 动态配置演示
    console.log('\n8. 动态配置演示:');
    
    // 动态更新基础URL
    Http.setBaseUrl('https://api.v2.example.com');
    console.log('更新基础URL:', Http.getBaseUrl());
    
    // 动态添加默认请求头
    Http.setDefaultHeaders({
        'X-API-Version': '2.0',
        'X-Client-ID': 'mobile-app'
    });
    console.log('更新后的默认请求头:', Http.getDefaultHeaders());
    
    console.log('\n=== HTTP配置演示完成 ===');
});

// 工具函数：根据环境获取配置
function getEnvironmentConfig() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return {
            baseUrl: 'http://localhost:8080',
            defaultHeaders: {
                'X-Environment': 'development',
                'X-Debug': 'true'
            }
        };
    } else if (hostname.includes('staging')) {
        return {
            baseUrl: 'https://api-staging.example.com',
            defaultHeaders: {
                'X-Environment': 'staging'
            }
        };
    } else {
        return {
            baseUrl: 'https://api.example.com',
            defaultHeaders: {
                'X-Environment': 'production'
            }
        };
    }
}

// 自动配置HTTP模块
Http.configure(getEnvironmentConfig());
