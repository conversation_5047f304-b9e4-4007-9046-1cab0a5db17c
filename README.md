# WebView Bridge Framework

一个强大、灵活的 Android WebView 与 JavaScript 双向通信框架，提供类型安全的 API 调用、事件系统、超时控制和请求取消等高级功能。

## 🚀 特性

- **🔄 双向通信**: JavaScript 与 Android 原生代码无缝交互
- **📦 模块化设计**: 支持自定义模块扩展，内置 HTTP、设备信息、UI、存储等模块
- **⏱️ 超时控制**: 前后端协调的超时机制，支持请求取消
- **🛡️ 类型安全**: 完整的错误处理和类型检查
- **🎯 事件系统**: 支持事件监听、一次性监听和批量清理
- **🌐 HTTP 增强**: 支持 baseUrl 配置、拦截器、AbortController 标准
- **🔧 易于扩展**: 简单的模块扩展机制
- **📱 生产就绪**: 完善的资源管理和错误处理

## 📋 目录

- [快速开始](#快速开始)
- [架构设计](#架构设计)
- [核心功能](#核心功能)
- [模块扩展](#模块扩展)
- [API 文档](#api-文档)
- [使用示例](#使用示例)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🏁 快速开始

### 1. Android 端集成

```java
public class MainActivity extends AppCompatActivity {
    private WebView webView;
    private BridgeManager bridgeManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        webView = findViewById(R.id.webview);
        bridgeManager = new BridgeManager(webView);

        // 配置 WebView
        WebSettings settings = webView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setDomStorageEnabled(true);

        // 加载页面
        webView.loadUrl("file:///android_asset/index.html");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (bridgeManager != null) {
            bridgeManager.destroy();
        }
    }
}
```

### 2. JavaScript 端使用

```html
<!DOCTYPE html>
<html>
  <head>
    <title>WebView Bridge Demo</title>
    <script src="bridge-core.js"></script>
    <script src="http-module.js"></script>
  </head>
  <body>
    <script>
      // 配置 HTTP 模块
      Http.configure({
        baseUrl: 'https://api.example.com',
        defaultHeaders: {
          'Content-Type': 'application/json',
        },
      })

      // 调用原生功能
      async function demo() {
        try {
          // 获取设备信息
          const deviceInfo = await BridgeCore.invoke('device', 'getDeviceInfo')
          console.log('设备信息:', deviceInfo)

          // 发起 HTTP 请求
          const response = await Http.get('/api/users')
          const users = await response.json()
          console.log('用户列表:', users)

          // 监听事件
          BridgeCore.addEventListener('network.changed', data => {
            console.log('网络状态变化:', data)
          })
        } catch (error) {
          console.error('操作失败:', error)
        }
      }

      demo()
    </script>
  </body>
</html>
```

## 🏗️ 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    JavaScript Layer                         │
├─────────────────────────────────────────────────────────────┤
│  BridgeCore  │  HttpModule  │  CustomModule  │  EventSystem │
├─────────────────────────────────────────────────────────────┤
│                    Bridge Interface                         │
├─────────────────────────────────────────────────────────────┤
│                    Android Layer                            │
├─────────────────────────────────────────────────────────────┤
│ BridgeManager │ HttpBridge │ DeviceBridge │ UIBridge │ ... │
├─────────────────────────────────────────────────────────────┤
│                   Native Android APIs                       │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### JavaScript 端

- **BridgeCore**: 核心通信引擎，处理方法调用、回调管理、超时控制
- **BridgeModule**: 模块基类，提供统一的模块接口
- **HttpModule**: HTTP 请求模块，支持 RESTful API 调用
- **RequestManager**: 请求生命周期管理，支持取消和超时

#### Android 端

- **BridgeManager**: 桥接管理器，负责模块注册和请求路由
- **BaseBridge**: 桥接模块基类，提供通用功能
- **CallbackManager**: 回调管理器，处理 JavaScript 回调
- **JsonUtils**: JSON 工具类，处理数据序列化

### 通信流程

```mermaid
sequenceDiagram
    participant JS as JavaScript
    participant BM as BridgeManager
    participant Module as Bridge Module
    participant Native as Native API

    JS->>BM: invoke(module, method, params)
    BM->>Module: processRequest(request)
    Module->>Native: call native API
    Native-->>Module: return result
    Module-->>BM: sendSuccess/sendError
    BM-->>JS: callback(response)
```

## ⚡ 核心功能

### 1. 方法调用

```javascript
// 基础调用
const result = await BridgeCore.invoke('module', 'method', params, options)

// 带超时的调用
const result = await BridgeCore.invoke('device', 'getInfo', {}, { timeout: 5000 })

// 模块方式调用
const deviceModule = new BridgeModule('device')
const info = await deviceModule.invoke('getInfo')
```

### 2. 事件系统

```javascript
// 监听事件
const unsubscribe = BridgeCore.addEventListener('network.changed', data => {
  console.log('网络状态:', data)
})

// 模块事件监听
const deviceModule = new BridgeModule('device')
const cancel = deviceModule.on('batteryChanged', data => {
  console.log('电池状态:', data)
})

// 一次性监听
deviceModule.once('ready', () => {
  console.log('设备就绪')
})

// 取消监听
unsubscribe()
cancel()
```

### 3. HTTP 请求

```javascript
// 配置
Http.configure({
  baseUrl: 'https://api.example.com',
  defaultHeaders: { Authorization: 'Bearer token' },
  defaultTimeout: 30000,
})

// 基础请求
const response = await Http.get('/users')
const users = await response.json()

// 可取消请求
const { promise, cancel } = Http.createCancelableRequest('/slow-api')
setTimeout(cancel, 5000) // 5秒后取消

try {
  const result = await promise
} catch (error) {
  if (error.type === 'ABORTED') {
    console.log('请求被取消')
  }
}
```

### 4. 超时和取消

```javascript
// 超时控制
try {
  const result = await BridgeCore.invoke('module', 'method', {}, { timeout: 10000 })
} catch (error) {
  if (error.type === 'TIMEOUT') {
    console.log('请求超时')
  }
}

// 手动取消
const callbackId = 'some-callback-id'
await BridgeCore.cancelRequest(callbackId)
```

## 🔧 模块扩展

### 创建自定义模块

#### 1. Android 端实现

```java
public class CustomBridge extends BaseBridge {
    private static final String MODULE_NAME = "custom";

    public CustomBridge(WebView webView) {
        super(webView);
    }

    @Override
    public String getModuleName() {
        return MODULE_NAME;
    }

    @Override
    protected void handleRequest(BridgeRequest request) {
        switch (request.getMethod()) {
            case "customMethod":
                handleCustomMethod(request);
                break;
            default:
                sendError(request.getCallbackId(), "METHOD_NOT_FOUND",
                         "Method not found: " + request.getMethod());
        }
    }

    private void handleCustomMethod(BridgeRequest request) {
        try {
            String param = request.getParam("param", "");

            // 执行自定义逻辑
            String result = performCustomOperation(param);

            JSONObject response = new JSONObject();
            response.put("result", result);
            sendSuccess(request.getCallbackId(), response);

        } catch (Exception e) {
            sendError(request.getCallbackId(), "CUSTOM_ERROR", e.getMessage());
        }
    }

    private String performCustomOperation(String param) {
        // 自定义业务逻辑
        return "Processed: " + param;
    }
}
```

#### 2. 注册模块

```java
// 在 BridgeManager 中注册
bridgeManager.registerBridge(new CustomBridge(webView));
```

#### 3. JavaScript 端使用

```javascript
// 创建模块实例
const customModule = new BridgeModule('custom')

// 调用自定义方法
const result = await customModule.invoke('customMethod', { param: 'test' })
console.log('结果:', result)

// 监听自定义事件
customModule.on('customEvent', data => {
  console.log('自定义事件:', data)
})
```

## 📚 API 文档

### BridgeCore

#### 静态方法

| 方法                                       | 描述               | 参数                                                                      | 返回值  |
| ------------------------------------------ | ------------------ | ------------------------------------------------------------------------- | ------- |
| `invoke(module, method, params, options)`  | 调用原生方法       | module: 模块名<br>method: 方法名<br>params: 参数对象<br>options: 选项配置 | Promise |
| `addEventListener(eventName, listener)`    | 添加事件监听器     | eventName: 事件名<br>listener: 监听函数                                   | void    |
| `removeEventListener(eventName, listener)` | 移除事件监听器     | eventName: 事件名<br>listener: 监听函数                                   | void    |
| `cancelRequest(callbackId)`                | 取消特定请求       | callbackId: 回调 ID                                                       | Promise |
| `clearPendingCallbacks()`                  | 清理所有待处理回调 | -                                                                         | void    |
| `getActiveRequestsCount()`                 | 获取活跃请求数量   | -                                                                         | number  |
| `getPendingCallbacksCount()`               | 获取待处理回调数量 | -                                                                         | number  |

#### 选项配置

```javascript
const options = {
  timeout: 30000, // 超时时间（毫秒）
  // 其他选项...
}
```

### HttpModule

#### 配置方法

| 方法                                  | 描述           | 参数                    |
| ------------------------------------- | -------------- | ----------------------- |
| `configure(config)`                   | 配置 HTTP 模块 | config: 配置对象        |
| `setBaseUrl(url)`                     | 设置基础 URL   | url: 基础 URL 字符串    |
| `getBaseUrl()`                        | 获取基础 URL   | -                       |
| `setDefaultHeaders(headers)`          | 设置默认请求头 | headers: 请求头对象     |
| `getDefaultHeaders()`                 | 获取默认请求头 | -                       |
| `addRequestInterceptor(interceptor)`  | 添加请求拦截器 | interceptor: 拦截器函数 |
| `addResponseInterceptor(interceptor)` | 添加响应拦截器 | interceptor: 拦截器函数 |

#### 请求方法

| 方法                                    | 描述           | 参数                               | 返回值                    |
| --------------------------------------- | -------------- | ---------------------------------- | ------------------------- |
| `fetch(url, options)`                   | 发起 HTTP 请求 | url: 请求 URL<br>options: 请求选项 | Promise\<HttpResponse\>   |
| `get(url, options)`                     | GET 请求       | url: 请求 URL<br>options: 请求选项 | Promise\<HttpResponse\>   |
| `post(url, options)`                    | POST 请求      | url: 请求 URL<br>options: 请求选项 | Promise\<HttpResponse\>   |
| `put(url, options)`                     | PUT 请求       | url: 请求 URL<br>options: 请求选项 | Promise\<HttpResponse\>   |
| `delete(url, options)`                  | DELETE 请求    | url: 请求 URL<br>options: 请求选项 | Promise\<HttpResponse\>   |
| `createCancelableRequest(url, options)` | 创建可取消请求 | url: 请求 URL<br>options: 请求选项 | {promise, cancel, signal} |

### BridgeModule

#### 实例方法

| 方法                              | 描述           | 参数                                            | 返回值              |
| --------------------------------- | -------------- | ----------------------------------------------- | ------------------- |
| `invoke(method, params, options)` | 调用模块方法   | method: 方法名<br>params: 参数<br>options: 选项 | Promise             |
| `on(eventName, listener)`         | 监听模块事件   | eventName: 事件名<br>listener: 监听器           | Function (取消函数) |
| `off(eventName, listener)`        | 移除事件监听   | eventName: 事件名<br>listener: 监听器           | void                |
| `once(eventName, listener)`       | 一次性事件监听 | eventName: 事件名<br>listener: 监听器           | Function (取消函数) |
| `removeAllListeners()`            | 移除所有监听器 | -                                               | void                |

## 💡 使用示例

### 完整应用示例

```javascript
class AppBridge {
  constructor() {
    this.init()
  }

  async init() {
    // 配置HTTP模块
    Http.configure({
      baseUrl: this.getApiBaseUrl(),
      defaultHeaders: {
        'Content-Type': 'application/json',
        'X-App-Version': await this.getAppVersion(),
      },
      defaultTimeout: 15000,
    })

    // 添加请求拦截器
    Http.addRequestInterceptor(this.addAuthHeader.bind(this))
    Http.addResponseInterceptor(this.handleApiErrors.bind(this))

    // 监听系统事件
    this.setupEventListeners()

    // 初始化应用
    await this.loadInitialData()
  }

  getApiBaseUrl() {
    const env = window.location.hostname
    if (env.includes('localhost')) return 'http://localhost:3000'
    if (env.includes('staging')) return 'https://api-staging.example.com'
    return 'https://api.example.com'
  }

  async getAppVersion() {
    try {
      const info = await BridgeCore.invoke('device', 'getAppInfo')
      return info.version
    } catch (error) {
      return '1.0.0'
    }
  }

  async addAuthHeader(config) {
    const token = await this.getAuthToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  }

  async handleApiErrors(response) {
    if (response.status === 401) {
      await this.handleUnauthorized()
    } else if (response.status >= 500) {
      await this.handleServerError()
    }
    return response
  }

  setupEventListeners() {
    // 网络状态监听
    BridgeCore.addEventListener('network.changed', data => {
      this.handleNetworkChange(data)
    })

    // 应用生命周期监听
    BridgeCore.addEventListener('app.pause', () => {
      this.handleAppPause()
    })

    BridgeCore.addEventListener('app.resume', () => {
      this.handleAppResume()
    })
  }

  async loadInitialData() {
    try {
      const [userInfo, settings] = await Promise.all([Http.get('/user/profile'), Http.get('/user/settings')])

      this.userInfo = await userInfo.json()
      this.settings = await settings.json()

      this.renderUI()
    } catch (error) {
      this.handleError('加载初始数据失败', error)
    }
  }

  async getAuthToken() {
    try {
      const result = await BridgeCore.invoke('storage', 'get', { key: 'auth_token' })
      return result.value
    } catch (error) {
      return null
    }
  }

  handleNetworkChange(data) {
    if (!data.connected) {
      this.showOfflineMessage()
    } else {
      this.hideOfflineMessage()
      this.syncPendingData()
    }
  }

  handleError(message, error) {
    console.error(message, error)
    BridgeCore.invoke('ui', 'showToast', {
      message: message,
      type: 'error',
    })
  }
}

// 启动应用
const app = new AppBridge()
```

### 错误处理最佳实践

```javascript
class ErrorHandler {
  static async handleBridgeError(error) {
    switch (error.type) {
      case 'TIMEOUT':
        return this.handleTimeout(error)
      case 'NETWORK_ERROR':
        return this.handleNetworkError(error)
      case 'BRIDGE_ERROR':
        return this.handleBridgeError(error)
      case 'ABORTED':
        return this.handleAborted(error)
      default:
        return this.handleUnknownError(error)
    }
  }

  static async handleTimeout(error) {
    console.warn('请求超时:', error.message)
    await BridgeCore.invoke('ui', 'showToast', {
      message: '请求超时，请检查网络连接',
      type: 'warning',
    })
  }

  static async handleNetworkError(error) {
    console.error('网络错误:', error.message)
    await BridgeCore.invoke('ui', 'showToast', {
      message: '网络连接失败，请稍后重试',
      type: 'error',
    })
  }
}

// 全局错误处理
window.addEventListener('unhandledrejection', event => {
  if (event.reason instanceof BridgeError) {
    ErrorHandler.handleBridgeError(event.reason)
    event.preventDefault()
  }
})
```

## 🎯 最佳实践

### 1. 错误处理

```javascript
// 统一错误处理
async function safeInvoke(module, method, params = {}, options = {}) {
  try {
    return await BridgeCore.invoke(module, method, params, options)
  } catch (error) {
    console.error(`调用失败: ${module}.${method}`, error)

    // 根据错误类型进行处理
    switch (error.type) {
      case 'TIMEOUT':
        throw new Error('操作超时，请稍后重试')
      case 'NETWORK_ERROR':
        throw new Error('网络连接失败')
      case 'BRIDGE_ERROR':
        throw new Error('系统错误，请重启应用')
      default:
        throw error
    }
  }
}
```

### 2. 资源管理

```javascript
class ResourceManager {
  constructor() {
    this.subscriptions = []
    this.requests = []
  }

  // 添加事件监听
  addEventListener(eventName, listener) {
    const unsubscribe = BridgeCore.addEventListener(eventName, listener)
    this.subscriptions.push(unsubscribe)
    return unsubscribe
  }

  // 添加请求
  addRequest(promise) {
    this.requests.push(promise)
    return promise
  }

  // 清理所有资源
  cleanup() {
    this.subscriptions.forEach(unsubscribe => unsubscribe())
    this.subscriptions = []

    // 取消所有待处理的请求
    BridgeCore.clearPendingCallbacks()
    this.requests = []
  }
}

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  resourceManager.cleanup()
})
```

### 3. 性能优化

```javascript
// 请求去重
class RequestDeduplicator {
  constructor() {
    this.pendingRequests = new Map()
  }

  async invoke(module, method, params = {}, options = {}) {
    const key = this.generateKey(module, method, params)

    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)
    }

    const promise = BridgeCore.invoke(module, method, params, options)
    this.pendingRequests.set(key, promise)

    try {
      const result = await promise
      this.pendingRequests.delete(key)
      return result
    } catch (error) {
      this.pendingRequests.delete(key)
      throw error
    }
  }

  generateKey(module, method, params) {
    return `${module}.${method}.${JSON.stringify(params)}`
  }
}
```

### 4. 配置管理

```javascript
class ConfigManager {
  constructor() {
    this.config = this.loadConfig()
    this.applyConfig()
  }

  loadConfig() {
    const env = this.getEnvironment()
    return {
      development: {
        apiBaseUrl: 'http://localhost:3000',
        timeout: 10000,
        debug: true,
      },
      staging: {
        apiBaseUrl: 'https://api-staging.example.com',
        timeout: 15000,
        debug: true,
      },
      production: {
        apiBaseUrl: 'https://api.example.com',
        timeout: 30000,
        debug: false,
      },
    }[env]
  }

  getEnvironment() {
    const hostname = window.location.hostname
    if (hostname.includes('localhost')) return 'development'
    if (hostname.includes('staging')) return 'staging'
    return 'production'
  }

  applyConfig() {
    Http.configure({
      baseUrl: this.config.apiBaseUrl,
      defaultTimeout: this.config.timeout,
      defaultHeaders: {
        'X-Environment': this.getEnvironment(),
      },
    })
  }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 桥接不可用

**问题**: `Android Bridge not available`

**解决方案**:

- 确保 WebView 已正确初始化
- 检查 JavaScript 是否已启用
- 确认 BridgeManager 已正确注册

```java
WebSettings settings = webView.getSettings();
settings.setJavaScriptEnabled(true);
settings.setDomStorageEnabled(true);
```

#### 2. 请求超时

**问题**: 请求频繁超时

**解决方案**:

- 检查网络连接
- 增加超时时间
- 检查 Android 端是否正确处理请求

```javascript
// 增加超时时间
const result = await BridgeCore.invoke('module', 'method', {}, { timeout: 60000 })
```

#### 3. 内存泄漏

**问题**: 应用内存使用持续增长

**解决方案**:

- 及时清理事件监听器
- 取消未完成的请求
- 正确管理资源生命周期

```javascript
// 页面卸载时清理
window.addEventListener('beforeunload', () => {
  BridgeCore.clearPendingCallbacks()
  // 清理其他资源...
})
```

#### 4. 事件监听器重复

**问题**: 事件被多次触发

**解决方案**:

- 使用 `once` 方法进行一次性监听
- 及时移除不需要的监听器
- 避免重复添加相同的监听器

```javascript
// 使用一次性监听
deviceModule.once('ready', () => {
  console.log('设备就绪')
})

// 保存监听器引用以便移除
const listener = data => console.log(data)
const unsubscribe = deviceModule.on('event', listener)

// 适当时机移除
unsubscribe()
```

### 调试技巧

#### 1. 启用调试模式

```javascript
// 在开发环境启用详细日志
if (window.location.hostname === 'localhost') {
  window.BRIDGE_DEBUG = true

  // 监听所有桥接调用
  const originalInvoke = BridgeCore.invoke
  BridgeCore.invoke = function (...args) {
    console.log('Bridge invoke:', args)
    return originalInvoke.apply(this, args)
  }
}
```

#### 2. 监控请求状态

```javascript
// 定期检查请求状态
setInterval(() => {
  console.log('活跃请求:', BridgeCore.getActiveRequestsCount())
  console.log('待处理回调:', BridgeCore.getPendingCallbacksCount())
}, 5000)
```

#### 3. 错误收集

```javascript
class ErrorCollector {
  constructor() {
    this.errors = []
    this.setupErrorHandling()
  }

  setupErrorHandling() {
    window.addEventListener('error', event => {
      this.collectError('JavaScript Error', event.error)
    })

    window.addEventListener('unhandledrejection', event => {
      this.collectError('Unhandled Promise Rejection', event.reason)
    })
  }

  collectError(type, error) {
    const errorInfo = {
      type,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    this.errors.push(errorInfo)

    // 发送错误报告
    this.reportError(errorInfo)
  }

  async reportError(errorInfo) {
    try {
      await Http.post('/api/errors', { body: errorInfo })
    } catch (e) {
      console.error('Failed to report error:', e)
    }
  }
}

const errorCollector = new ErrorCollector()
```

## 🚀 可优化点

### 1. 性能优化

- **请求缓存**: 实现智能缓存机制，避免重复请求
- **批量操作**: 支持批量调用，减少桥接开销
- **懒加载**: 按需加载模块，减少初始化时间
- **连接池**: 实现连接池管理，提高并发性能

### 2. 功能增强

- **离线支持**: 添加离线缓存和同步机制
- **数据压缩**: 支持请求/响应数据压缩
- **加密传输**: 添加端到端加密支持
- **版本控制**: 支持 API 版本管理和兼容性检查

### 3. 开发体验

- **TypeScript 支持**: 提供完整的类型定义
- **调试工具**: 开发专用的调试面板
- **文档生成**: 自动生成 API 文档
- **测试框架**: 提供单元测试和集成测试工具

### 4. 监控和分析

- **性能监控**: 添加详细的性能指标收集
- **错误追踪**: 完善的错误报告和分析系统
- **使用统计**: 收集 API 使用情况和性能数据
- **实时监控**: 提供实时的系统状态监控

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：

- 提交 Issue
- 发送邮件至 <EMAIL>
- 查看文档: https://docs.example.com
