/**
 * HTTP请求模块 - 基于桥接框架的HTTP封装
 */
;(function (window) {
  'use strict'

  /**
   * HTTP模块类
   */
  class HttpModule extends BridgeModule {
    constructor() {
      super('http')
      this.activeRequests = new Map() // 跟踪活跃的请求
      this.config = {
        baseUrl: '',
        defaultHeaders: {},
        defaultTimeout: 30000,
        interceptors: {
          request: [],
          response: [],
        },
      }
    }

    /**
     * 配置HTTP模块
     * @param {Object} config - 配置对象
     * @param {string} config.baseUrl - 基础URL
     * @param {Object} config.defaultHeaders - 默认请求头
     * @param {number} config.defaultTimeout - 默认超时时间
     */
    configure(config = {}) {
      if (config.baseUrl !== undefined) {
        this.config.baseUrl = config.baseUrl.replace(/\/$/, '') // 移除末尾斜杠
      }
      if (config.defaultHeaders) {
        this.config.defaultHeaders = { ...this.config.defaultHeaders, ...config.defaultHeaders }
      }
      if (config.defaultTimeout !== undefined) {
        this.config.defaultTimeout = config.defaultTimeout
      }
    }

    /**
     * 设置基础URL
     * @param {string} baseUrl - 基础URL
     */
    setBaseUrl(baseUrl) {
      this.config.baseUrl = baseUrl.replace(/\/$/, '')
    }

    /**
     * 获取基础URL
     * @returns {string} 基础URL
     */
    getBaseUrl() {
      return this.config.baseUrl
    }

    /**
     * 设置默认请求头
     * @param {Object} headers - 请求头对象
     */
    setDefaultHeaders(headers) {
      this.config.defaultHeaders = { ...this.config.defaultHeaders, ...headers }
    }

    /**
     * 获取默认请求头
     * @returns {Object} 默认请求头
     */
    getDefaultHeaders() {
      return { ...this.config.defaultHeaders }
    }

    /**
     * 构建完整的URL
     * @param {string} url - 相对或绝对URL
     * @returns {string} 完整的URL
     */
    buildUrl(url) {
      // 如果是绝对URL（包含协议），直接返回
      if (/^https?:\/\//.test(url)) {
        return url
      }

      // 如果没有baseUrl，直接返回原URL
      if (!this.config.baseUrl) {
        return url
      }

      // 构建完整URL
      const cleanUrl = url.replace(/^\//, '') // 移除开头斜杠
      return `${this.config.baseUrl}/${cleanUrl}`
    }

    /**
     * 添加请求拦截器
     * @param {Function} interceptor - 拦截器函数
     */
    addRequestInterceptor(interceptor) {
      this.config.interceptors.request.push(interceptor)
    }

    /**
     * 添加响应拦截器
     * @param {Function} interceptor - 拦截器函数
     */
    addResponseInterceptor(interceptor) {
      this.config.interceptors.response.push(interceptor)
    }

    /**
     * 发起HTTP请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<HttpResponse>}
     */
    async fetch(url, options = {}) {
      const {
        method = 'GET',
        headers = {},
        body = null,
        timeout = this.config.defaultTimeout,
        signal,
        skipInterceptors = false,
      } = options

      // 构建完整URL
      const fullUrl = this.buildUrl(url)

      // 合并默认请求头和自定义请求头
      const mergedHeaders = { ...this.config.defaultHeaders, ...headers }

      // 构建请求参数
      let requestConfig = {
        method: method.toUpperCase(),
        url: fullUrl,
        headers: mergedHeaders,
        body: body ? (typeof body === 'object' ? body : { data: body }) : null,
      }

      // 应用请求拦截器
      if (!skipInterceptors) {
        for (const interceptor of this.config.interceptors.request) {
          try {
            requestConfig = (await interceptor(requestConfig)) || requestConfig
          } catch (error) {
            throw new HttpError(`Request interceptor error: ${error.message}`, 'INTERCEPTOR_ERROR')
          }
        }
      }

      // 创建可取消的请求
      const requestPromise = this.invoke('request', requestConfig, { timeout })

      // 如果提供了 AbortSignal，设置取消逻辑
      if (signal) {
        const abortHandler = () => {
          // 这里需要获取到 callbackId 来取消请求
          // 由于当前架构限制，我们先抛出取消错误
          throw new HttpError('Request aborted', 'ABORTED')
        }

        if (signal.aborted) {
          throw new HttpError('Request aborted', 'ABORTED')
        }

        signal.addEventListener('abort', abortHandler)

        try {
          const responseData = await requestPromise
          signal.removeEventListener('abort', abortHandler)

          let response = new HttpResponse(responseData)

          // 应用响应拦截器
          if (!skipInterceptors) {
            for (const interceptor of this.config.interceptors.response) {
              try {
                response = (await interceptor(response)) || response
              } catch (error) {
                throw new HttpError(`Response interceptor error: ${error.message}`, 'INTERCEPTOR_ERROR')
              }
            }
          }

          return response
        } catch (error) {
          signal.removeEventListener('abort', abortHandler)
          throw new HttpError(error.message, error.type)
        }
      } else {
        try {
          const responseData = await requestPromise
          let response = new HttpResponse(responseData)

          // 应用响应拦截器
          if (!skipInterceptors) {
            for (const interceptor of this.config.interceptors.response) {
              try {
                response = (await interceptor(response)) || response
              } catch (error) {
                throw new HttpError(`Response interceptor error: ${error.message}`, 'INTERCEPTOR_ERROR')
              }
            }
          }

          return response
        } catch (error) {
          throw new HttpError(error.message, error.type)
        }
      }
    }

    /**
     * 创建可取消的请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Object} 包含 promise 和 cancel 方法的对象
     */
    createCancelableRequest(url, options = {}) {
      const controller = new AbortController()
      const promise = this.fetch(url, { ...options, signal: controller.signal })

      return {
        promise,
        cancel: () => controller.abort(),
        signal: controller.signal,
      }
    }

    // 便捷方法
    get(url, options = {}) {
      return this.fetch(url, { ...options, method: 'GET' })
    }

    post(url, options = {}) {
      return this.fetch(url, { ...options, method: 'POST' })
    }

    put(url, options = {}) {
      return this.fetch(url, { ...options, method: 'PUT' })
    }

    delete(url, options = {}) {
      return this.fetch(url, { ...options, method: 'DELETE' })
    }
  }

  /**
   * HTTP响应类
   */
  class HttpResponse {
    constructor(data) {
      this.status = data.status
      this.statusText = data.statusText
      this.ok = data.ok
      this.headers = data.headers
      this._body = data.body
    }

    async text() {
      return this._body || ''
    }

    async json() {
      try {
        return JSON.parse(this._body || '{}')
      } catch (error) {
        throw new Error('Invalid JSON response')
      }
    }
  }

  /**
   * HTTP错误类
   */
  class HttpError extends BridgeError {
    constructor(message, type = 'HTTP_ERROR') {
      super(message, type, 'http')
    }
  }

  // 创建全局实例
  window.Http = new HttpModule()
  window.HttpResponse = HttpResponse
  window.HttpError = HttpError
})(window)
