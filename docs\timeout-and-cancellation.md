# 超时和中断处理机制

本文档详细说明了 WebView Bridge 框架中的超时控制和请求中断机制，以及 Web 端和 Android 端如何协调处理这些场景。

## 📋 目录

- [概述](#概述)
- [超时机制](#超时机制)
- [中断机制](#中断机制)
- [前后端协调](#前后端协调)
- [实现细节](#实现细节)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)

## 🎯 概述

### 设计目标

1. **统一的超时控制**: 前后端协调的超时机制，避免资源浪费
2. **主动中断支持**: 支持用户主动取消正在进行的操作
3. **资源管理**: 确保超时或中断后正确清理资源
4. **错误处理**: 提供清晰的错误信息和处理机制

### 核心概念

- **超时 (Timeout)**: 操作在指定时间内未完成时自动终止
- **中断 (Cancellation)**: 用户或系统主动终止正在进行的操作
- **回调管理**: 跟踪和管理所有待处理的回调
- **资源清理**: 确保操作终止后正确释放资源

## ⏱️ 超时机制

### 1. 前端超时控制

#### 基础实现

```javascript
// BridgeCore 中的超时实现
static invoke(module, method, params = {}, options = {}) {
    return new Promise((resolve, reject) => {
        const callbackId = `callback_${++callbackCounter}_${Date.now()}`;
        const timeout = options.timeout || 30000;

        // 设置超时定时器
        const timeoutId = setTimeout(async () => {
            // 超时时尝试取消 Android 端的请求
            await requestManager.cancelRequest(callbackId);
            pendingCallbacks.delete(callbackId);
            reject(new BridgeError('Request timeout', 'TIMEOUT', module));
        }, timeout);

        // 存储回调和超时信息
        pendingCallbacks.set(callbackId, {
            resolve: (data) => {
                requestManager.removeRequest(callbackId);
                pendingCallbacks.delete(callbackId);
                resolve(data);
            },
            reject: (error) => {
                requestManager.removeRequest(callbackId);
                pendingCallbacks.delete(callbackId);
                reject(error);
            }
        });

        // 将请求添加到管理器
        requestManager.addRequest(callbackId, module, method, timeoutId);

        // 传递超时时间给 Android 端
        const enhancedParams = {
            ...params,
            _bridgeTimeout: timeout
        };

        window.AndroidBridge.invoke(module, method, JSON.stringify(enhancedParams), callbackId);
    });
}
```

#### 请求管理器

```javascript
const requestManager = {
  activeRequests: new Map(),

  addRequest(callbackId, module, method, timeoutId) {
    this.activeRequests.set(callbackId, {
      module,
      method,
      timeoutId,
      timestamp: Date.now(),
    })
  },

  removeRequest(callbackId) {
    const request = this.activeRequests.get(callbackId)
    if (request) {
      clearTimeout(request.timeoutId)
      this.activeRequests.delete(callbackId)
    }
    return request
  },

  async cancelRequest(callbackId) {
    const request = this.activeRequests.get(callbackId)
    if (request) {
      try {
        // 通知 Android 端取消请求
        await BridgeCore.invoke(request.module, 'cancel', { callbackId }, { timeout: 5000 })
      } catch (error) {
        console.warn('Failed to cancel request on Android side:', error)
      }
      this.removeRequest(callbackId)
    }
  },
}
```

### 2. Android 端超时处理

#### BaseBridge 中的超时支持

```java
public abstract class BaseBridge implements IBridge {
    protected final Map<String, Future<?>> activeTasks = new ConcurrentHashMap<>();

    public final void processRequest(BridgeRequest request) {
        // 获取前端传递的超时时间
        int bridgeTimeout = request.getParam("_bridgeTimeout", 30000);

        // 在线程池中执行，支持超时控制
        Future<?> future = executorService.submit(() -> {
            try {
                handleRequest(request);
            } catch (Exception e) {
                Log.e(TAG, "Error handling request: " + request, e);
                if (request.hasCallback()) {
                    callbackManager.sendError(request.getCallbackId(), "BRIDGE_ERROR", e.getMessage());
                }
            }
        });

        // 存储任务引用以便取消
        activeTasks.put(request.getCallbackId(), future);

        // 设置超时取消
        scheduledExecutor.schedule(() -> {
            if (!future.isDone()) {
                future.cancel(true);
                activeTasks.remove(request.getCallbackId());
                if (request.hasCallback()) {
                    callbackManager.sendError(request.getCallbackId(), "TIMEOUT",
                        "Request timeout after " + bridgeTimeout + "ms");
                }
            }
        }, bridgeTimeout, TimeUnit.MILLISECONDS);
    }
}
```

#### HTTP 模块的超时实现

```java
public class HttpBridge extends BaseBridge {
    private void handleHttpRequest(BridgeRequest request) {
        try {
            int bridgeTimeout = request.getParam("_bridgeTimeout", 30000);

            // 创建带超时的客户端
            OkHttpClient timeoutClient = httpClient.newBuilder()
                    .connectTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                    .readTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                    .writeTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                    .build();

            Call call = timeoutClient.newCall(httpRequest);
            activeCalls.put(request.getCallbackId(), call);

            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    activeCalls.remove(request.getCallbackId());
                    if (call.isCanceled()) {
                        sendError(request.getCallbackId(), "CANCELLED", "Request was cancelled");
                    } else {
                        sendError(request.getCallbackId(), "NETWORK_ERROR", e.getMessage());
                    }
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    activeCalls.remove(request.getCallbackId());
                    // 处理响应...
                }
            });

        } catch (Exception e) {
            sendError(request.getCallbackId(), "REQUEST_ERROR", e.getMessage());
        }
    }
}
```

## 🛑 中断机制

### 1. 前端中断支持

#### AbortController 集成

```javascript
// HTTP 模块中的中断支持
class HttpModule extends BridgeModule {
  async fetch(url, options = {}) {
    const { signal, ...otherOptions } = options

    if (signal && signal.aborted) {
      throw new HttpError('Request aborted', 'ABORTED')
    }

    const requestPromise = this.invoke('request', params, otherOptions)

    if (signal) {
      const abortHandler = () => {
        // 这里可以通过 callbackId 取消请求
        throw new HttpError('Request aborted', 'ABORTED')
      }

      signal.addEventListener('abort', abortHandler)

      try {
        const result = await requestPromise
        signal.removeEventListener('abort', abortHandler)
        return result
      } catch (error) {
        signal.removeEventListener('abort', abortHandler)
        throw error
      }
    }

    return requestPromise
  }

  createCancelableRequest(url, options = {}) {
    const controller = new AbortController()
    const promise = this.fetch(url, { ...options, signal: controller.signal })

    return {
      promise,
      cancel: () => controller.abort(),
      signal: controller.signal,
    }
  }
}
```

#### 手动取消

```javascript
// 通过 BridgeCore 手动取消请求
class RequestController {
  constructor() {
    this.activeRequests = new Map()
  }

  async invoke(module, method, params = {}, options = {}) {
    const promise = BridgeCore.invoke(module, method, params, options)
    const requestId = this.generateRequestId()

    this.activeRequests.set(requestId, {
      promise,
      module,
      method,
      timestamp: Date.now(),
    })

    try {
      const result = await promise
      this.activeRequests.delete(requestId)
      return result
    } catch (error) {
      this.activeRequests.delete(requestId)
      throw error
    }
  }

  async cancelRequest(requestId) {
    const request = this.activeRequests.get(requestId)
    if (request) {
      try {
        await BridgeCore.cancelRequest(requestId)
        this.activeRequests.delete(requestId)
        return true
      } catch (error) {
        console.error('Failed to cancel request:', error)
        return false
      }
    }
    return false
  }

  cancelAllRequests() {
    const promises = Array.from(this.activeRequests.keys()).map(id => this.cancelRequest(id))
    return Promise.allSettled(promises)
  }
}
```

### 2. Android 端中断处理

#### 通用取消机制

```java
public abstract class BaseBridge implements IBridge {
    protected void handleCancelRequest(BridgeRequest request) {
        String targetCallbackId = request.getParam("callbackId", "");

        if (targetCallbackId.isEmpty()) {
            sendError(request.getCallbackId(), "INVALID_CALLBACK_ID", "Callback ID cannot be empty");
            return;
        }

        // 取消对应的任务
        Future<?> task = activeTasks.get(targetCallbackId);
        if (task != null) {
            boolean cancelled = task.cancel(true);
            activeTasks.remove(targetCallbackId);

            JSONObject result = new JSONObject();
            try {
                result.put("cancelled", cancelled);
                result.put("callbackId", targetCallbackId);
                sendSuccess(request.getCallbackId(), result);
            } catch (JSONException e) {
                sendError(request.getCallbackId(), "JSON_ERROR", e.getMessage());
            }
        } else {
            JSONObject result = new JSONObject();
            try {
                result.put("cancelled", false);
                result.put("reason", "Request not found or already completed");
                sendSuccess(request.getCallbackId(), result);
            } catch (JSONException e) {
                sendError(request.getCallbackId(), "JSON_ERROR", e.getMessage());
            }
        }
    }
}
```

#### HTTP 请求取消

```java
public class HttpBridge extends BaseBridge {
    private final Map<String, Call> activeCalls = new ConcurrentHashMap<>();

    private void handleCancelRequest(BridgeRequest request) {
        String targetCallbackId = request.getParam("callbackId", "");

        Call call = activeCalls.get(targetCallbackId);
        if (call != null) {
            call.cancel();
            activeCalls.remove(targetCallbackId);

            JSONObject result = new JSONObject();
            try {
                result.put("cancelled", true);
                result.put("callbackId", targetCallbackId);
                sendSuccess(request.getCallbackId(), result);
            } catch (JSONException e) {
                sendError(request.getCallbackId(), "JSON_ERROR", e.getMessage());
            }
        } else {
            // 请求不存在或已完成
            JSONObject result = new JSONObject();
            try {
                result.put("cancelled", false);
                result.put("reason", "HTTP request not found or already completed");
                sendSuccess(request.getCallbackId(), result);
            } catch (JSONException e) {
                sendError(request.getCallbackId(), "JSON_ERROR", e.getMessage());
            }
        }
    }

    @Override
    public void destroy() {
        super.destroy();

        // 取消所有活跃的 HTTP 请求
        for (Call call : activeCalls.values()) {
            if (call != null && !call.isCanceled()) {
                call.cancel();
            }
        }
        activeCalls.clear();
    }
}
```

## 🤝 前后端协调

### 1. 超时时间同步

#### 前端传递超时参数

```javascript
// 前端在调用时传递超时时间
const result = await BridgeCore.invoke(
  'http',
  'request',
  {
    url: 'https://api.example.com/data',
    method: 'GET',
  },
  {
    timeout: 15000, // 15秒超时
  }
)

// BridgeCore 内部会将超时时间添加到参数中
const enhancedParams = {
  url: 'https://api.example.com/data',
  method: 'GET',
  _bridgeTimeout: 15000, // 传递给 Android 端
}
```

#### Android 端接收和应用

```java
public class HttpBridge extends BaseBridge {
    private void handleHttpRequest(BridgeRequest request) {
        // 获取前端传递的超时时间
        int bridgeTimeout = request.getParam("_bridgeTimeout", 30000);

        // 应用到 HTTP 客户端
        OkHttpClient timeoutClient = httpClient.newBuilder()
                .connectTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                .readTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                .writeTimeout(bridgeTimeout, TimeUnit.MILLISECONDS)
                .build();

        // 同时设置任务级别的超时
        Future<?> future = executorService.submit(() -> {
            // 执行 HTTP 请求
        });

        // 设置超时取消
        scheduledExecutor.schedule(() -> {
            if (!future.isDone()) {
                future.cancel(true);
                sendError(request.getCallbackId(), "TIMEOUT",
                    "Request timeout after " + bridgeTimeout + "ms");
            }
        }, bridgeTimeout, TimeUnit.MILLISECONDS);
    }
}
```

### 2. 统一错误处理

#### 前端错误类型

```javascript
const ErrorTypes = {
  TIMEOUT: 'TIMEOUT',
  CANCELLED: 'CANCELLED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  BRIDGE_ERROR: 'BRIDGE_ERROR',
  INVALID_PARAMS: 'INVALID_PARAMS',
  METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',
}

class BridgeError extends Error {
  constructor(message, type = 'UNKNOWN_ERROR', module = null) {
    super(message)
    this.name = 'BridgeError'
    this.type = type
    this.module = module
    this.timestamp = new Date().toISOString()
  }
}
```

#### Android 端错误类型

```java
public class BridgeErrorTypes {
    public static final String TIMEOUT = "TIMEOUT";
    public static final String CANCELLED = "CANCELLED";
    public static final String NETWORK_ERROR = "NETWORK_ERROR";
    public static final String BRIDGE_ERROR = "BRIDGE_ERROR";
    public static final String INVALID_PARAMS = "INVALID_PARAMS";
    public static final String METHOD_NOT_FOUND = "METHOD_NOT_FOUND";
}
```

## 💡 最佳实践

### 1. 超时时间设置

```javascript
// 根据操作类型设置合适的超时时间
const TimeoutConfig = {
  QUICK: 5000, // 5秒 - 快速操作
  STANDARD: 15000, // 15秒 - 标准操作
  LONG: 60000, // 60秒 - 长时间操作
  FILE_TRANSFER: 300000, // 5分钟 - 文件传输
}

// 使用示例
const deviceInfo = await BridgeCore.invoke(
  'device',
  'getInfo',
  {},
  {
    timeout: TimeoutConfig.QUICK,
  }
)
```

### 2. 优雅的取消处理

```javascript
class OperationManager {
  constructor() {
    this.operations = new Map()
  }

  async startOperation(name, operationFn, options = {}) {
    const controller = new AbortController()
    const { timeout = 30000 } = options

    // 设置超时
    const timeoutId = setTimeout(() => {
      controller.abort()
    }, timeout)

    const operation = {
      controller,
      timeoutId,
      startTime: Date.now(),
    }

    this.operations.set(name, operation)

    try {
      const result = await operationFn(controller.signal)
      clearTimeout(timeoutId)
      this.operations.delete(name)
      return result
    } catch (error) {
      clearTimeout(timeoutId)
      this.operations.delete(name)

      if (error.name === 'AbortError') {
        throw new BridgeError('Operation cancelled', 'CANCELLED')
      }
      throw error
    }
  }

  cancelOperation(name) {
    const operation = this.operations.get(name)
    if (operation) {
      operation.controller.abort()
      clearTimeout(operation.timeoutId)
      this.operations.delete(name)
      return true
    }
    return false
  }
}
```

### 3. 资源清理

```javascript
// 页面级别的资源管理
class PageResourceManager {
  constructor() {
    this.resources = {
      requests: new Set(),
      listeners: new Set(),
      timers: new Set(),
    }

    this.setupCleanup()
  }

  setupCleanup() {
    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
      this.cleanup()
    })

    // 页面隐藏时清理（移动端）
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.cleanup()
      }
    })
  }

  cleanup() {
    // 清理请求
    BridgeCore.clearPendingCallbacks()

    // 清理事件监听器
    this.resources.listeners.forEach(cleanup => cleanup())
    this.resources.listeners.clear()

    // 清理定时器
    this.resources.timers.forEach(timerId => clearTimeout(timerId))
    this.resources.timers.clear()

    console.log('页面资源已清理')
  }
}
```

## 🔧 故障排除

### 常见问题

#### 1. 超时问题诊断

**问题**: 请求频繁超时

**解决方案**:

- 检查网络连接状态
- 增加超时时间设置
- 检查 Android 端处理逻辑
- 使用诊断工具分析

```javascript
// 超时诊断工具
class TimeoutDiagnostics {
  static async diagnoseTimeout(module, method, params = {}) {
    const timeouts = [5000, 10000, 30000, 60000]

    for (const timeout of timeouts) {
      try {
        console.log(`尝试超时时间: ${timeout}ms`)
        const result = await BridgeCore.invoke(module, method, params, { timeout })
        console.log(`成功完成，耗时: ${Date.now() - startTime}ms`)
        return { success: true, timeout }
      } catch (error) {
        if (error.type === 'TIMEOUT') {
          console.log(`超时失败: ${timeout}ms`)
          continue
        } else {
          return { success: false, error }
        }
      }
    }

    return { success: false, error: 'All timeouts failed' }
  }
}
```

#### 2. 取消机制验证

**问题**: 取消操作不生效

**解决方案**:

- 确认 Android 端实现了取消逻辑
- 检查 callbackId 是否正确传递
- 验证取消时机是否合适

```javascript
// 取消机制测试
class CancellationTest {
  static async testCancellation() {
    const { promise, cancel } = Http.createCancelableRequest('/slow-endpoint')

    // 2秒后取消
    setTimeout(() => {
      console.log('执行取消操作...')
      cancel()
    }, 2000)

    try {
      await promise
      return false // 不应该成功
    } catch (error) {
      return error.type === 'ABORTED' // 应该被取消
    }
  }
}
```

## 📊 总结

通过实现统一的超时和中断机制，WebView Bridge 框架能够：

1. **提供可靠的超时控制**: 前后端协调确保操作不会无限期等待
2. **支持优雅的取消操作**: 用户可以主动取消不需要的操作
3. **确保资源正确清理**: 超时或取消后自动清理相关资源
4. **提供清晰的错误信息**: 统一的错误类型和处理机制
5. **支持性能监控**: 内置的监控和诊断工具

这些机制共同确保了框架的健壮性和用户体验，特别是在网络不稳定或长时间操作的场景下。
